#!/usr/bin/env python3
"""
Complete Enhanced Server for AI Navigator Scrapers
Provides both traditional scraping and enhanced processing capabilities
"""

import logging
import sys
import os
import time
import json
from threading import Thread
from flask import Flask, request, jsonify

# Add the project root to Python path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from flask_cors import CORS
    from ai_navigator_client import AINavigatorClient
    from enhanced_item_processor import EnhancedItemProcessor
    from backend.enrichment import DataEnrichmentService
    from enhanced_taxonomy_service import EnhancedTaxonomyService
    from scraper_pipeline import ScraperPipeline
except ImportError as e:
    print(f"Import error: {e}")
    print("Please ensure all required modules are installed")
    sys.exit(1)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('complete_enhanced_server.log'),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

# Initialize Flask app
app = Flask(__name__)
CORS(app, origins=['http://localhost:3000', 'http://127.0.0.1:3000'])

# Global services
scraper_pipeline = None
ai_client = None
taxonomy_service = None
item_processor = None

# Job tracking
jobs = {}
enhanced_jobs = {}

def initialize_services():
    """Initialize all services needed for the server"""
    global scraper_pipeline, ai_client, taxonomy_service, item_processor
    
    try:
        logger.info("🔧 Initializing services...")
        
        # Initialize core services with cost optimization
        ai_client = AINavigatorClient()

        # COST OPTIMIZATION: Initialize enrichment service with cost-aware settings
        enrichment_service = DataEnrichmentService("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")

        # ENABLE COST OPTIMIZATION BY DEFAULT to reduce Perplexity API costs
        enrichment_service.set_cost_optimization(True)

        taxonomy_service = EnhancedTaxonomyService(ai_client)
        item_processor = EnhancedItemProcessor(ai_client, enrichment_service, taxonomy_service)
        
        # Initialize scraper pipeline
        scraper_pipeline = ScraperPipeline()
        
        logger.info("✅ All services initialized successfully")
        return True
        
    except Exception as e:
        logger.error(f"❌ Error initializing services: {str(e)}")
        return False

# ===============================
# TRADITIONAL SCRAPING ENDPOINTS
# ===============================

@app.route('/api/start-scraping', methods=['POST'])
def start_scraping():
    """Start the scraping process with enhanced feedback and pagination support"""
    try:
        data = request.get_json() or {}
        max_items = data.get('max_items', 100)  # Default to 100 items
        spider_name = data.get('spider', 'futuretools')  # Default to futuretools
        start_page = data.get('start_page', 1)  # Default to page 1
        max_pages = data.get('max_pages', 10)  # Default to 10 pages

        logger.info(f"Starting scraping with:")
        logger.info(f"  Spider: {spider_name}")
        logger.info(f"  Max items: {max_items}")
        logger.info(f"  Start page: {start_page}")
        logger.info(f"  Max pages: {max_pages}")

        # Run the spider with the specified parameters
        result = scraper_pipeline.run_spider(
            spider_name,
            max_items=max_items,
            start_page=start_page,
            max_pages=max_pages
        )
        
        if result['success']:
            stats = result['stats']
            
            # Create detailed feedback message
            message_parts = []
            message_parts.append(f"Scraping completed successfully!")
            message_parts.append(f"✅ Created: {stats.get('successful_submissions', 0)} new entities")
            
            if stats.get('duplicates_skipped', 0) > 0:
                message_parts.append(f"⏭️  Skipped: {stats.get('duplicates_skipped', 0)} duplicates")
                
                # Add breakdown of skip reasons if available
                url_skips = stats.get('url_duplicates_skipped', 0)
                name_skips = stats.get('name_duplicates_skipped', 0)
                
                if url_skips > 0:
                    message_parts.append(f"   - {url_skips} URL duplicates")
                if name_skips > 0:
                    message_parts.append(f"   - {name_skips} name duplicates")
            
            if stats.get('processing_errors', 0) > 0:
                message_parts.append(f"❌ Errors: {stats.get('processing_errors', 0)} processing errors")
            
            return jsonify({
                'success': True,
                'message': '\n'.join(message_parts),
                'stats': {
                    **stats,
                    'skip_details': stats.get('skip_reasons', [])[:10]  # Show first 10 skip reasons
                }
            })
        else:
            return jsonify({
                'success': False,
                'message': f"Scraping failed: {result.get('error', 'Unknown error')}",
                'stats': result.get('stats', {})
            }), 500
            
    except Exception as e:
        logger.error(f"Error starting scraping: {str(e)}")
        return jsonify({
            'success': False,
            'message': f"Error starting scraping: {str(e)}"
        }), 500

def run_traditional_scraping(spider_name, max_items):
    """Run traditional scraping with the scraper pipeline"""
    try:
        logger.info(f"🕷️ Running traditional scraper: scrapy crawl {spider_name}")
        
        # Use the scraper pipeline to run the spider with max_items
        result = scraper_pipeline.run_spider(spider_name, max_items)
        
        if result['success']:
            logger.info(f"✅ Traditional scraping completed: {spider_name}")
            logger.info(f"📊 Stats: {json.dumps(result['stats'], indent=2)}")
        else:
            logger.error(f"❌ Traditional scraping failed: {result.get('error', 'Unknown error')}")
        
    except Exception as e:
        logger.error(f"❌ Error in traditional scraping: {str(e)}")

# ===============================
# ENHANCED SCRAPING ENDPOINTS
# ===============================

@app.route('/api/start-enhanced-scraping', methods=['POST'])
def start_enhanced_scraping():
    """Start enhanced scraping job with AI processing"""
    try:
        data = request.json
        tools = data.get('tools', [])
        use_parallel = data.get('use_parallel', False)
        use_phase3 = data.get('use_phase3', False)

        if not tools or not isinstance(tools, list) or len(tools) == 0:
            return jsonify({
                "success": False,
                "error": "Invalid tool data provided. Please provide a list of tools."
            }), 400

        # Generate job ID
        job_id = f"enhanced_{int(time.time())}"
        
        # Initialize job tracking
        enhanced_jobs[job_id] = {
            'status': 'running',
            'progress': 0,
            'total_tools': len(tools),
            'phase3_enabled': use_phase3,
            'processing_mode': 'parallel' if use_parallel else 'sequential',
            'start_time': time.time(),
            'results': None,
            'error': None
        }

        # Start processing in background thread
        thread = Thread(target=process_enhanced_tools, args=(job_id, tools, use_parallel, use_phase3))
        thread.start()

        logger.info(f"🚀 Started enhanced scraping job {job_id} with {len(tools)} tools")

        return jsonify({
            "success": True,
            "job_id": job_id,
            "message": f"Enhanced scraping job started for {len(tools)} tools",
            "total_tools": len(tools),
            "processing_mode": 'parallel' if use_parallel else 'sequential',
            "phase3_enabled": use_phase3,
            "estimated_time": len(tools) * 2.0  # Estimate 2 seconds per tool
        })

    except Exception as e:
        logger.error(f"❌ Error starting enhanced scraping: {str(e)}")
        return jsonify({
            "success": False,
            "error": f"Failed to start enhanced scraping: {str(e)}"
        }), 500

def process_enhanced_tools(job_id, tools, use_parallel, use_phase3):
    """Process tools with enhanced AI capabilities and taxonomy mapping"""
    try:
        logger.info(f"🔄 Processing enhanced job {job_id} with {len(tools)} tools")
        
        results = {
            'successful_results': [],
            'failed_results': [],
            'total_tools': len(tools),
            'success_rate': 0.0,
            'database_save_rate': 0.0,
            'total_processing_time': 0.0
        }
        
        start_time = time.time()
        
        for i, tool in enumerate(tools):
            try:
                logger.info(f"🔍 Processing tool {i+1}/{len(tools)}: {tool.get('name', 'Unknown')}")
                
                # Update progress
                progress = (i / len(tools)) * 100
                enhanced_jobs[job_id]['progress'] = progress
                
                # Process the tool using enhanced item processor with taxonomy
                lead_data = {
                    'tool_name_on_directory': tool.get('name', ''),
                    'external_website_url': tool.get('url', ''),
                    'source_directory': 'enhanced_processing',
                    'scraped_date': time.strftime('%Y-%m-%d %H:%M:%S')
                }
                
                # Enhanced processing with taxonomy mapping
                entity_dto = item_processor.process_lead_item(lead_data)
                
                if entity_dto:
                    # Submit to AI Navigator API
                    result = ai_client.create_entity(entity_dto)
                    
                    if result:
                        results['successful_results'].append({
                            'name': tool.get('name'),
                            'url': tool.get('url'),
                            'entity_id': result.get('id') if isinstance(result, dict) else None,
                            'categories_mapped': len(entity_dto.get('category_ids', [])),
                            'tags_mapped': len(entity_dto.get('tag_ids', [])),
                            'features_mapped': len(entity_dto.get('feature_ids', [])),
                            'processing_time': time.time() - start_time,
                            'data': result  # Include the actual entity data for debugging
                        })
                        logger.info(f"✅ Successfully processed {tool.get('name')} with {len(entity_dto.get('category_ids', []))} categories, {len(entity_dto.get('tag_ids', []))} tags, {len(entity_dto.get('feature_ids', []))} features")
                    else:
                        results['failed_results'].append({
                            'name': tool.get('name'),
                            'url': tool.get('url'),
                            'error': 'Failed to create entity in database'
                        })
                        logger.error(f"❌ Failed to save {tool.get('name')} to database")
                else:
                    results['failed_results'].append({
                        'name': tool.get('name'),
                        'url': tool.get('url'),
                        'error': 'Failed to process with enhanced item processor'
                    })
                    logger.error(f"❌ Failed to process {tool.get('name')}")
                
                # Small delay to prevent overwhelming APIs
                time.sleep(0.5)
                
            except Exception as e:
                logger.error(f"❌ Error processing tool {tool.get('name', 'Unknown')}: {str(e)}")
                results['failed_results'].append({
                    'name': tool.get('name', 'Unknown'),
                    'url': tool.get('url', ''),
                    'error': str(e)
                })
        
        # Calculate final stats
        total_time = time.time() - start_time
        successful_count = len(results['successful_results'])
        total_count = len(tools)
        
        results['success_rate'] = (successful_count / total_count) * 100 if total_count > 0 else 0
        results['database_save_rate'] = (successful_count / total_count) * 100 if total_count > 0 else 0
        results['total_processing_time'] = total_time
        
        # Update job status
        enhanced_jobs[job_id].update({
            'status': 'completed',
            'progress': 100,
            'results': results,
            'end_time': time.time()
        })
        
        logger.info(f"✅ Enhanced job {job_id} completed successfully")
        logger.info(f"📊 Results: {successful_count}/{total_count} successful ({results['success_rate']:.1f}%)")
        
    except Exception as e:
        logger.error(f"❌ Error in enhanced processing job {job_id}: {str(e)}")
        enhanced_jobs[job_id].update({
            'status': 'failed',
            'error': str(e),
            'end_time': time.time()
        })

# ===============================
# STATUS AND MONITORING ENDPOINTS
# ===============================

@app.route('/api/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    try:
        return jsonify({
            'status': 'healthy',
            'server': 'complete_enhanced_server',
            'timestamp': time.time(),
            'traditional_pipeline': scraper_pipeline is not None,
            'enhanced_pipeline': item_processor is not None,
            'phase3_available': taxonomy_service is not None
        })
    except Exception as e:
        logger.error(f"❌ Error in health check: {str(e)}")
        return jsonify({
            'status': 'unhealthy',
            'error': str(e)
        }), 500

@app.route('/api/spiders', methods=['GET'])
def get_spiders():
    """Get available spiders"""
    try:
        if scraper_pipeline:
            spiders = scraper_pipeline.get_available_spiders()
        else:
            spiders = ['futuretools', 'futuretools_complete', 'futuretools_highvolume', 'futuretools_all', 'futuretools_mega', 'futuretools_ultra', 'taaft']
            
        return jsonify({
            'spiders': spiders
        })
    except Exception as e:
        logger.error(f"❌ Error getting spiders: {str(e)}")
        return jsonify({
            "error": f"Failed to get spiders: {str(e)}"
        }), 500

@app.route('/api/capabilities', methods=['GET'])
def get_capabilities():
    """Get system capabilities"""
    try:
        return jsonify({
            'enhanced_scraping': True,
            'traditional_scraping': True,
            'phase3_features': {
                'structured_data_extraction': True,
                'advanced_content_analysis': True,
                'performance_analysis': True,
                'parallel_processing': True,
                'taxonomy_mapping': True,
                'ai_enhancement': True
            },
            'available_spiders': scraper_pipeline.get_available_spiders() if scraper_pipeline else ['futuretools', 'futuretools_complete', 'futuretools_highvolume', 'taaft']
        })
    except Exception as e:
        logger.error(f"❌ Error getting capabilities: {str(e)}")
        return jsonify({
            "error": f"Failed to get capabilities: {str(e)}"
        }), 500

@app.route('/api/job-status/<job_id>', methods=['GET'])
def get_job_status(job_id):
    """Get status of enhanced job"""
    try:
        if job_id in enhanced_jobs:
            return jsonify(enhanced_jobs[job_id])
        else:
            return jsonify({
                "error": "Job not found"
            }), 404
    except Exception as e:
        logger.error(f"❌ Error getting job status: {str(e)}")
        return jsonify({
            "error": f"Failed to get job status: {str(e)}"
        }), 500

@app.route('/api/missing-taxonomy', methods=['GET'])
def get_missing_taxonomy():
    """Get missing taxonomy items"""
    try:
        if taxonomy_service:
            missing_items = taxonomy_service.get_missing_items()
            return jsonify({
                'missing_items': missing_items,
                'count': len(missing_items)
            })
        else:
            return jsonify({
                'missing_items': [],
                'count': 0,
                'message': 'Taxonomy service not initialized'
            })
    except Exception as e:
        logger.error(f"❌ Error getting missing taxonomy: {str(e)}")
        return jsonify({
            "error": f"Failed to get missing taxonomy: {str(e)}"
        }), 500

@app.route('/api/retry-failed-entities', methods=['POST'])
def retry_failed_entities():
    """Retry entities that failed to be created due to API errors"""
    try:
        logger.info("🔄 Starting retry of failed entities...")

        # Use the AI Navigator client to retry failed entities
        results = ai_client.retry_failed_entities()

        logger.info(f"✅ Retry completed: {results}")

        return jsonify({
            'status': 'success',
            'message': 'Failed entities retry completed',
            'results': results
        })

    except Exception as e:
        logger.error(f"Error retrying failed entities: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500

@app.route('/api/status', methods=['GET'])
def get_status():
    """Get overall system status"""
    try:
        pipeline_status = scraper_pipeline.get_status() if scraper_pipeline else {
            'is_running': False,
            'current_job': None,
            'stats': {}
        }

        return jsonify({
            'server': 'complete_enhanced_server',
            'status': 'running',
            'pipeline_status': pipeline_status,
            'enhanced_jobs_count': len(enhanced_jobs),
            'services_initialized': scraper_pipeline is not None
        })
    except Exception as e:
        logger.error(f"❌ Error getting status: {str(e)}")
        return jsonify({
            "error": f"Failed to get status: {str(e)}"
        }), 500

@app.route('/api/logs')
def get_logs():
    """Get recent logs"""
    try:
        lines = request.args.get('lines', 100, type=int)
        # For now, return empty logs - you can implement actual log reading later
        return jsonify({
            'logs': [],
            'message': 'Log endpoint available - implement log file reading if needed'
        })
    except Exception as e:
        logger.error(f"❌ Error getting logs: {str(e)}")
        return jsonify({
            "error": f"Failed to get logs: {str(e)}"
        }), 500

@app.route('/api/jobs')
def get_jobs():
    """Get all jobs status"""
    try:
        jobs = []

        # Add traditional pipeline job if running
        if scraper_pipeline and scraper_pipeline.is_running:
            pipeline_status = scraper_pipeline.get_status()
            jobs.append({
                'id': pipeline_status.get('current_job', 'unknown'),
                'type': 'traditional_scraping',
                'status': 'running',
                'stats': pipeline_status.get('stats', {})
            })

        # Add enhanced jobs
        for job_id, job_data in enhanced_jobs.items():
            jobs.append({
                'id': job_id,
                'type': 'enhanced_processing',
                'status': job_data.get('status', 'unknown'),
                'stats': job_data.get('stats', {})
            })

        return jsonify({
            'jobs': jobs,
            'total_jobs': len(jobs)
        })
    except Exception as e:
        logger.error(f"❌ Error getting jobs: {str(e)}")
        return jsonify({
            "error": f"Failed to get jobs: {str(e)}"
        }), 500

@app.route('/api/performance-dashboard')
def get_performance_dashboard():
    """Get performance dashboard data"""
    try:
        # Return basic performance data
        return jsonify({
            'performance': {
                'total_jobs': len(enhanced_jobs),
                'active_jobs': len([j for j in enhanced_jobs.values() if j.get('status') == 'running']),
                'completed_jobs': len([j for j in enhanced_jobs.values() if j.get('status') == 'completed']),
                'traditional_pipeline_running': scraper_pipeline.is_running if scraper_pipeline else False
            },
            'message': 'Performance dashboard data'
        })
    except Exception as e:
        logger.error(f"❌ Error getting performance dashboard: {str(e)}")
        return jsonify({
            "error": f"Failed to get performance dashboard: {str(e)}"
        }), 500

# ===============================
# MAIN EXECUTION
# ===============================

if __name__ == '__main__':
    start_time = time.time()
    
    logger.info("🚀 Starting Complete Enhanced Server")
    logger.info("=" * 50)
    
    # Initialize services
    if not initialize_services():
        logger.error("❌ Failed to initialize services. Exiting.")
        sys.exit(1)
    
    logger.info("🌐 Server Features:")
    logger.info("   ✅ Traditional Scraping (with Max Items support)")
    logger.info("   ✅ Enhanced AI Processing")
    logger.info("   ✅ Automatic Taxonomy Mapping")
    logger.info("   ✅ Phase 3 Capabilities")
    logger.info("   ✅ Real-time Job Monitoring")
    
    logger.info("\n🔧 Available Endpoints:")
    logger.info("   POST /api/start-scraping - Traditional scraping with maxItems")
    logger.info("   POST /api/start-enhanced-scraping - Enhanced processing with taxonomy")
    logger.info("   GET  /api/job-status/<job_id> - Job status")
    logger.info("   GET  /api/health - Health check")
    logger.info("   GET  /api/spiders - Available spiders")
    logger.info("   GET  /api/capabilities - System capabilities")
    logger.info("   GET  /api/missing-taxonomy - Missing taxonomy items")
    
    try:
        logger.info("\n🎉 Complete Enhanced Server starting on http://localhost:8001")
        app.run(host='0.0.0.0', port=8001, debug=False, threaded=True)
    except Exception as e:
        logger.error(f"❌ Failed to start server: {str(e)}")
        sys.exit(1)
